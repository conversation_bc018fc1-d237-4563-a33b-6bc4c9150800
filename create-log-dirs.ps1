# ========================================
# PowerShell 日志目录创建脚本
# 功能：为项目创建必要的日志目录
# 作者：系统管理员
# 更新时间：2025-08-04
# ========================================

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "         创建日志目录结构脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 获取当前目录
$currentDir = Get-Location
Write-Host "当前工作目录: $currentDir" -ForegroundColor Yellow
Write-Host ""

# 定义要创建的目录列表
$directories = @(
    "ssp-jm-supervise\logs",
    "ssp-jm-supervise\logs\info",
    "ssp-jm-supervise\logs\error",
    "ssp-jm-supervise\logs\job",
    "ssp-jm-bsp\logs",
    "ssp-jm-bsp\logs\info",
    "ssp-jm-bsp\logs\error",
    "ssp-jm-bsp\logs\job",
    "logs",
    "logs\info",
    "logs\error",
    "logs\job"
)

# 创建目录函数
function Create-Directory {
    param($path)
    
    if (Test-Path $path) {
        Write-Host "    ✓ $path 已存在" -ForegroundColor Gray
        return $true
    } else {
        try {
            New-Item -ItemType Directory -Path $path -Force | Out-Null
            Write-Host "    ✓ 创建 $path" -ForegroundColor Green
            return $true
        } catch {
            Write-Host "    ✗ 创建 $path 失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
}

# 按模块分组创建目录
Write-Host "[1/3] 创建监管模块日志目录..." -ForegroundColor Cyan
$superviseDirectories = $directories | Where-Object { $_ -like "ssp-jm-supervise*" }
foreach ($dir in $superviseDirectories) {
    Create-Directory $dir | Out-Null
}

Write-Host ""
Write-Host "[2/3] 创建BSP模块日志目录..." -ForegroundColor Cyan
$bspDirectories = $directories | Where-Object { $_ -like "ssp-jm-bsp*" }
foreach ($dir in $bspDirectories) {
    Create-Directory $dir | Out-Null
}

Write-Host ""
Write-Host "[3/3] 创建根目录日志目录..." -ForegroundColor Cyan
$rootDirectories = $directories | Where-Object { $_ -like "logs*" -and $_ -notlike "*ssp-jm*" }
foreach ($dir in $rootDirectories) {
    Create-Directory $dir | Out-Null
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           目录创建完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

Write-Host "创建的目录结构如下：" -ForegroundColor Yellow
Write-Host ""
Write-Host "├── logs\" -ForegroundColor White
Write-Host "│   ├── info\" -ForegroundColor White
Write-Host "│   ├── error\" -ForegroundColor White
Write-Host "│   └── job\" -ForegroundColor White
Write-Host "├── ssp-jm-supervise\logs\" -ForegroundColor White
Write-Host "│   ├── info\" -ForegroundColor White
Write-Host "│   ├── error\" -ForegroundColor White
Write-Host "│   └── job\" -ForegroundColor White
Write-Host "└── ssp-jm-bsp\logs\" -ForegroundColor White
Write-Host "    ├── info\" -ForegroundColor White
Write-Host "    ├── error\" -ForegroundColor White
Write-Host "    └── job\" -ForegroundColor White
Write-Host ""

Write-Host "注意事项：" -ForegroundColor Yellow
Write-Host "1. 请确保应用程序对这些目录有写权限" -ForegroundColor White
Write-Host "2. 如果某些目录创建失败，请检查是否有足够的权限" -ForegroundColor White
Write-Host "3. 可以手动创建失败的目录" -ForegroundColor White
Write-Host ""

# 验证目录是否创建成功
$successCount = 0
$totalCount = $directories.Count

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        $successCount++
    }
}

Write-Host "目录创建统计：$successCount/$totalCount 个目录创建成功" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } else { "Yellow" })
Write-Host ""

Write-Host "按任意键退出..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
