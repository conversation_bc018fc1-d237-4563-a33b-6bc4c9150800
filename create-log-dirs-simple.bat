@echo off
REM 简化版日志目录创建脚本
REM 解决闪退问题

echo 开始创建日志目录...
echo.

REM 创建监管模块日志目录
echo 创建监管模块日志目录...
md "ssp-jm-supervise\logs" 2>nul
md "ssp-jm-supervise\logs\info" 2>nul
md "ssp-jm-supervise\logs\error" 2>nul
md "ssp-jm-supervise\logs\job" 2>nul

REM 创建BSP模块日志目录
echo 创建BSP模块日志目录...
md "ssp-jm-bsp\logs" 2>nul
md "ssp-jm-bsp\logs\info" 2>nul
md "ssp-jm-bsp\logs\error" 2>nul
md "ssp-jm-bsp\logs\job" 2>nul

REM 创建根目录日志目录
echo 创建根目录日志目录...
md "logs" 2>nul
md "logs\info" 2>nul
md "logs\error" 2>nul
md "logs\job" 2>nul

echo.
echo 日志目录创建完成！
echo.
echo 创建的目录包括：
echo - ssp-jm-supervise\logs\info
echo - ssp-jm-supervise\logs\error
echo - ssp-jm-supervise\logs\job
echo - ssp-jm-bsp\logs\info
echo - ssp-jm-bsp\logs\error
echo - ssp-jm-bsp\logs\job
echo - logs\info
echo - logs\error
echo - logs\job
echo.
echo 如果看到"系统找不到指定的路径"的错误，这是正常的，
echo 表示父目录不存在，脚本会自动创建。
echo.
echo 请按任意键继续...
pause
