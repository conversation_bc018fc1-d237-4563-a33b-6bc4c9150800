# 日志配置优化说明

## 概述
本次优化实现了按日期每天生成一个日志文件的功能，并添加了详细的中文注释。

## 优化内容

### 1. 监管模块 (ssp-jm-supervise)
**配置文件**: `ssp-jm-supervise/src/main/resources/log4j.properties`

#### 主要改进：
- 修复了配置属性大小写不一致的问题（datePattern -> DatePattern）
- 优化了日志文件命名规则，使其更加清晰
- 添加了详细的中文注释
- 实现了按日志级别分类存储

#### 日志文件结构：
```
logs/
├── supervise.log                    # 主日志文件（所有级别）
├── info/
│   └── supervise-info.log          # INFO级别及以上日志
├── error/
│   └── supervise-error.log         # ERROR级别日志
└── job/
    └── supervise-job.log           # 定时任务日志
```

### 2. BSP模块 (ssp-jm-bsp)
**配置文件**: `ssp-jm-bsp/src/main/resources/log4j.properties`

#### 主要改进：
- 统一了配置格式和命名规范
- 添加了详细的中文注释
- 优化了第三方框架的日志级别设置

#### 日志文件结构：
```
logs/
├── ssp-bsp.log                     # 主日志文件（所有级别）
├── info/
│   └── ssp-bsp-info.log           # INFO级别及以上日志
├── error/
│   └── ssp-bsp-error.log          # ERROR级别日志
└── job/
    └── ssp-bsp-job.log            # 定时任务日志
```

## 日志滚动机制

### 按天滚动
- 使用 `DailyRollingFileAppender` 实现按天滚动
- 日期模式：`'.'yyyy-MM-dd`
- 当天的日志文件不带日期后缀
- 历史日志文件自动添加日期后缀

### 示例：
```
# 当天（2025-08-04）
supervise.log

# 历史文件
supervise.log.2025-08-03
supervise.log.2025-08-02
supervise.log.2025-08-01
```

## 日志级别配置

### 监管模块日志级别：
- 根日志级别：INFO
- Spring框架：INFO
- 项目包(com.inspur)：INFO
- MyBatis：ERROR
- Dubbo：INFO

### BSP模块日志级别：
- 根日志级别：INFO
- Spring框架：ERROR
- 项目包(com.inspur)：DEBUG（便于开发调试）
- MyBatis：DEBUG（便于SQL调试）
- Dubbo：ERROR/DEBUG（根据组件不同）

## 日志格式

### 控制台输出格式：
```
%d{yyyy-MM-dd HH:mm:ss}[%t][%c][%p]-%m%n
```
示例：`2025-08-04 10:30:15[main][com.inspur.service.UserService][INFO]-用户登录成功`

### 文件输出格式：
```
%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n
```
示例：`2025-08-04 10:30:15  INFO UserService:25 - 用户登录成功`

## 使用说明

### 1. 目录创建
在项目运行前，请确保以下目录存在：
```bash
mkdir -p logs/info
mkdir -p logs/error
mkdir -p logs/job
```

### 2. 权限设置
确保应用程序对logs目录及其子目录有写权限。

### 3. 日志清理
建议定期清理历史日志文件，可以通过以下方式：
- 手动删除过期的日志文件
- 使用定时任务自动清理
- 配置日志轮转工具

## 注意事项

1. **目录权限**：确保应用程序对日志目录有写权限
2. **磁盘空间**：定期监控日志文件大小，避免占用过多磁盘空间
3. **性能影响**：大量日志输出可能影响应用性能，建议在生产环境适当调整日志级别
4. **字符编码**：日志文件默认使用系统字符编码，如需指定编码请在配置中添加相应设置

## 配置验证

### 验证步骤：
1. 启动应用程序
2. 检查logs目录是否自动创建
3. 检查各级别日志文件是否正常生成
4. 验证日志内容格式是否正确
5. 测试日志滚动功能（可修改系统时间验证）

### 常见问题：
1. **日志文件未生成**：检查目录权限和路径配置
2. **日志级别不生效**：检查logger配置和additivity设置
3. **中文乱码**：检查系统字符编码设置

## 后续优化建议

1. **日志压缩**：考虑对历史日志文件进行压缩存储
2. **日志分析**：集成日志分析工具，便于问题排查
3. **监控告警**：对ERROR级别日志设置监控告警
4. **性能优化**：考虑使用异步日志输出提升性能
