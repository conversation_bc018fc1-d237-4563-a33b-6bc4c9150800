# ========================================
# Log4j ?????? - BSP?? (ssp-jm-bsp)
# ??????????????????
# ????????
# ?????2025-08-04
# ========================================

# ????????
# ????????INFO???????(CONSOLE)???(file)
log4j.rootLogger=INFO,CONSOLE,file

# ========================================
# ???????
# ========================================
log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
# ????????????[???][??][????]-????
log4j.appender.CONSOLE.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss}[%t][%c][%p]-%m%n

# ========================================
# ??????? - ????
# ========================================
log4j.appender.file=org.apache.log4j.DailyRollingFileAppender
# ????????????????
log4j.appender.file.File=./logs/ssp-bsp.log
# ??????????????????????????
# ???ssp-bsp.log.2025-08-03, ssp-bsp.log.2025-08-02
log4j.appender.file.DatePattern='.'yyyy-MM-dd
# ?????????
log4j.appender.file.Append=true
# ??????????? ???? ??:?? - ????
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n

# ========================================
# INFO???????? - ????
# ========================================
log4j.appender.info=org.apache.log4j.DailyRollingFileAppender
# INFO????????
log4j.appender.info.File=./logs/info/ssp-bsp-info.log
# ????????????????
log4j.appender.info.DatePattern='.'yyyy-MM-dd
# ???INFO????????
log4j.appender.info.Threshold=INFO
# ????
log4j.appender.info.Append=true
log4j.appender.info.layout=org.apache.log4j.PatternLayout
log4j.appender.info.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n

# ========================================
# ERROR???????? - ????
# ========================================
log4j.appender.error=org.apache.log4j.DailyRollingFileAppender
# ERROR????????
log4j.appender.error.File=./logs/error/ssp-bsp-error.log
# ????????????????
log4j.appender.error.DatePattern='.'yyyy-MM-dd
# ???ERROR?????
log4j.appender.error.Threshold=ERROR
# ????
log4j.appender.error.Append=true
log4j.appender.error.layout=org.apache.log4j.PatternLayout
log4j.appender.error.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n

# ========================================
# ?????????? - ????
# ========================================
log4j.appender.job=org.apache.log4j.DailyRollingFileAppender
# ??????????
log4j.appender.job.File=./logs/job/ssp-bsp-job.log
# ????????????????
log4j.appender.job.DatePattern='.'yyyy-MM-dd
# ????
log4j.appender.job.Append=true
log4j.appender.job.layout=org.apache.log4j.PatternLayout
log4j.appender.job.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %5p %c{1}:%L - %m%n

# ========================================
# ??????????
# ========================================
# INFO???????????info??
log4j.logger.info=INFO,info
log4j.additivity.info=false

# ERROR???????????error??
log4j.logger.error=ERROR,error
log4j.additivity.error=false

# ?????????????job??
log4j.logger.job=INFO,job
log4j.additivity.job=false

# ========================================
# ???????????
# ========================================
# Spring?????????ERROR???????
log4j.logger.org.springframework=ERROR
# ?????????????DEBUG???????
log4j.logger.com.inspur=DEBUG
# ?????????????ERROR
log4j.logger.org=ERROR
# MyBatis???????DEBUG???SQL??
log4j.logger.org.apache.ibatis=DEBUG
# Dubbo???????????DEBUG
log4j.logger.com.alibaba.dubbo.config=DEBUG
# Dubbo?????????ERROR
log4j.logger.com.alibaba.dubbo=ERROR
# Dubbo????????????DEBUG
log4j.logger.com.alibaba.dubbo.config.AbstractConfig=DEBUG
# Dubbo Zookeeper???????????ERROR
log4j.logger.com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry=ERROR
# Jangod?????????????
#log4j.logger.org.jangod.iweb=DEBUG

# ========================================
# ??????
# ========================================
# 1. ./logs/ssp-bsp.log - ???????????????
# 2. ./logs/info/ssp-bsp-info.log - INFO????????
# 3. ./logs/error/ssp-bsp-error.log - ???ERROR?????
# 4. ./logs/job/ssp-bsp-job.log - ?????????
#
# ????????????????.yyyy-MM-dd
# ???ssp-bsp.log.2025-08-03, ssp-bsp-info.log.2025-08-03
#
# ??????logs????????info?error?job????
# ??????????????

