#!/bin/bash
# ========================================
# 创建日志目录结构脚本
# 功能：为项目创建必要的日志目录
# 作者：系统管理员
# 更新时间：2025-08-04
# ========================================

echo "正在创建日志目录结构..."

# 创建监管模块日志目录
echo "创建监管模块日志目录..."
mkdir -p ssp-jm-supervise/logs/info
mkdir -p ssp-jm-supervise/logs/error
mkdir -p ssp-jm-supervise/logs/job

# 创建BSP模块日志目录
echo "创建BSP模块日志目录..."
mkdir -p ssp-jm-bsp/logs/info
mkdir -p ssp-jm-bsp/logs/error
mkdir -p ssp-jm-bsp/logs/job

# 创建根目录日志目录（如果需要）
echo "创建根目录日志目录..."
mkdir -p logs/info
mkdir -p logs/error
mkdir -p logs/job

# 设置目录权限
echo "设置目录权限..."
chmod -R 755 logs/
chmod -R 755 ssp-jm-supervise/logs/
chmod -R 755 ssp-jm-bsp/logs/

echo ""
echo "日志目录结构创建完成！"
echo ""
echo "目录结构如下："
echo "├── logs/"
echo "│   ├── info/"
echo "│   ├── error/"
echo "│   └── job/"
echo "├── ssp-jm-supervise/logs/"
echo "│   ├── info/"
echo "│   ├── error/"
echo "│   └── job/"
echo "└── ssp-jm-bsp/logs/"
echo "    ├── info/"
echo "    ├── error/"
echo "    └── job/"
echo ""
echo "目录权限已设置为755，应用程序应该可以正常写入日志文件。"
