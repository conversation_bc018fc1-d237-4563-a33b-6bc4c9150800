package com.inspur.test;

import org.apache.log4j.Logger;

/**
 * 日志配置测试类
 * 功能：测试优化后的日志配置是否正常工作
 * 作者：系统管理员
 * 更新时间：2025-08-04
 */
public class LogConfigTest {
    
    // 获取不同类型的日志记录器
    private static final Logger logger = Logger.getLogger(LogConfigTest.class);
    private static final Logger infoLogger = Logger.getLogger("info");
    private static final Logger errorLogger = Logger.getLogger("error");
    private static final Logger jobLogger = Logger.getLogger("job");
    
    public static void main(String[] args) {
        System.out.println("开始测试日志配置...");
        
        // 测试主日志记录器
        testMainLogger();
        
        // 测试INFO级别日志记录器
        testInfoLogger();
        
        // 测试ERROR级别日志记录器
        testErrorLogger();
        
        // 测试定时任务日志记录器
        testJobLogger();
        
        System.out.println("日志配置测试完成！");
        System.out.println("请检查以下目录中的日志文件：");
        System.out.println("1. ./logs/supervise.log - 主日志文件");
        System.out.println("2. ./logs/info/supervise-info.log - INFO级别日志");
        System.out.println("3. ./logs/error/supervise-error.log - ERROR级别日志");
        System.out.println("4. ./logs/job/supervise-job.log - 定时任务日志");
    }
    
    /**
     * 测试主日志记录器
     */
    private static void testMainLogger() {
        logger.debug("这是一条DEBUG级别的日志消息");
        logger.info("这是一条INFO级别的日志消息");
        logger.warn("这是一条WARN级别的日志消息");
        logger.error("这是一条ERROR级别的日志消息");
        logger.fatal("这是一条FATAL级别的日志消息");
    }
    
    /**
     * 测试INFO级别日志记录器
     */
    private static void testInfoLogger() {
        infoLogger.info("用户登录成功 - 用户ID: 12345");
        infoLogger.info("数据查询操作 - 查询条件: name='张三'");
        infoLogger.warn("系统负载较高 - CPU使用率: 85%");
        infoLogger.error("数据库连接异常 - 连接池已满");
    }
    
    /**
     * 测试ERROR级别日志记录器
     */
    private static void testErrorLogger() {
        try {
            // 模拟一个异常
            int result = 10 / 0;
        } catch (Exception e) {
            errorLogger.error("计算异常", e);
        }
        
        errorLogger.error("系统启动失败 - 配置文件缺失");
        errorLogger.error("网络连接超时 - 目标服务器无响应");
        errorLogger.fatal("系统崩溃 - 内存溢出");
    }
    
    /**
     * 测试定时任务日志记录器
     */
    private static void testJobLogger() {
        jobLogger.info("定时任务开始执行 - 任务名称: 数据同步任务");
        jobLogger.info("正在处理数据 - 当前进度: 50%");
        jobLogger.warn("处理速度较慢 - 预计剩余时间: 30分钟");
        jobLogger.info("定时任务执行完成 - 处理记录数: 1000");
        jobLogger.error("定时任务执行失败 - 数据源连接异常");
    }
}
