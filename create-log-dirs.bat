@echo off
REM ========================================
REM 创建日志目录结构脚本
REM 功能：为项目创建必要的日志目录
REM 作者：系统管理员
REM 更新时间：2025-08-04
REM ========================================

echo 正在创建日志目录结构...

REM 创建监管模块日志目录
echo 创建监管模块日志目录...
if not exist "logs" mkdir "logs"
if not exist "logs\info" mkdir "logs\info"
if not exist "logs\error" mkdir "logs\error"
if not exist "logs\job" mkdir "logs\job"

echo.
echo 日志目录结构创建完成！
echo.
echo 目录结构如下：
echo ├── logs/
echo │   ├── info/
echo │   ├── error/
echo │   └── job/
echo ├── ssp-jm-supervise/logs/
echo │   ├── info/
echo │   ├── error/
echo │   └── job/
echo └── ssp-jm-bsp/logs/
echo     ├── info/
echo     ├── error/
echo     └── job/
echo.
echo 请确保应用程序对这些目录有写权限。
pause
