@echo off
chcp 65001 >nul 2>&1
REM ========================================
REM 创建日志目录结构脚本
REM 功能：为项目创建必要的日志目录
REM 作者：系统管理员
REM 更新时间：2025-08-04
REM ========================================

title 创建日志目录结构
color 0A

echo.
echo ========================================
echo           创建日志目录结构脚本
echo ========================================
echo.

REM 检查当前目录
echo 当前工作目录: %CD%
echo.

REM 创建监管模块日志目录
echo [1/3] 创建监管模块日志目录...
if not exist "ssp-jm-supervise\logs" (
    mkdir "ssp-jm-supervise\logs" 2>nul
    if exist "ssp-jm-supervise\logs" (
        echo     ✓ 创建 ssp-jm-supervise\logs
    ) else (
        echo     ✗ 创建 ssp-jm-supervise\logs 失败
    )
) else (
    echo     ✓ ssp-jm-supervise\logs 已存在
)

if not exist "ssp-jm-supervise\logs\info" (
    mkdir "ssp-jm-supervise\logs\info" 2>nul
    if exist "ssp-jm-supervise\logs\info" (
        echo     ✓ 创建 ssp-jm-supervise\logs\info
    ) else (
        echo     ✗ 创建 ssp-jm-supervise\logs\info 失败
    )
) else (
    echo     ✓ ssp-jm-supervise\logs\info 已存在
)

if not exist "ssp-jm-supervise\logs\error" (
    mkdir "ssp-jm-supervise\logs\error" 2>nul
    if exist "ssp-jm-supervise\logs\error" (
        echo     ✓ 创建 ssp-jm-supervise\logs\error
    ) else (
        echo     ✗ 创建 ssp-jm-supervise\logs\error 失败
    )
) else (
    echo     ✓ ssp-jm-supervise\logs\error 已存在
)

if not exist "ssp-jm-supervise\logs\job" (
    mkdir "ssp-jm-supervise\logs\job" 2>nul
    if exist "ssp-jm-supervise\logs\job" (
        echo     ✓ 创建 ssp-jm-supervise\logs\job
    ) else (
        echo     ✗ 创建 ssp-jm-supervise\logs\job 失败
    )
) else (
    echo     ✓ ssp-jm-supervise\logs\job 已存在
)

echo.

REM 创建BSP模块日志目录
echo [2/3] 创建BSP模块日志目录...
if not exist "ssp-jm-bsp\logs" (
    mkdir "ssp-jm-bsp\logs" 2>nul
    if exist "ssp-jm-bsp\logs" (
        echo     ✓ 创建 ssp-jm-bsp\logs
    ) else (
        echo     ✗ 创建 ssp-jm-bsp\logs 失败
    )
) else (
    echo     ✓ ssp-jm-bsp\logs 已存在
)

if not exist "ssp-jm-bsp\logs\info" (
    mkdir "ssp-jm-bsp\logs\info" 2>nul
    if exist "ssp-jm-bsp\logs\info" (
        echo     ✓ 创建 ssp-jm-bsp\logs\info
    ) else (
        echo     ✗ 创建 ssp-jm-bsp\logs\info 失败
    )
) else (
    echo     ✓ ssp-jm-bsp\logs\info 已存在
)

if not exist "ssp-jm-bsp\logs\error" (
    mkdir "ssp-jm-bsp\logs\error" 2>nul
    if exist "ssp-jm-bsp\logs\error" (
        echo     ✓ 创建 ssp-jm-bsp\logs\error
    ) else (
        echo     ✗ 创建 ssp-jm-bsp\logs\error 失败
    )
) else (
    echo     ✓ ssp-jm-bsp\logs\error 已存在
)

if not exist "ssp-jm-bsp\logs\job" (
    mkdir "ssp-jm-bsp\logs\job" 2>nul
    if exist "ssp-jm-bsp\logs\job" (
        echo     ✓ 创建 ssp-jm-bsp\logs\job
    ) else (
        echo     ✗ 创建 ssp-jm-bsp\logs\job 失败
    )
) else (
    echo     ✓ ssp-jm-bsp\logs\job 已存在
)

echo.

REM 创建根目录日志目录（如果需要）
echo [3/3] 创建根目录日志目录...
if not exist "logs" (
    mkdir "logs" 2>nul
    if exist "logs" (
        echo     ✓ 创建 logs
    ) else (
        echo     ✗ 创建 logs 失败
    )
) else (
    echo     ✓ logs 已存在
)

if not exist "logs\info" (
    mkdir "logs\info" 2>nul
    if exist "logs\info" (
        echo     ✓ 创建 logs\info
    ) else (
        echo     ✗ 创建 logs\info 失败
    )
) else (
    echo     ✓ logs\info 已存在
)

if not exist "logs\error" (
    mkdir "logs\error" 2>nul
    if exist "logs\error" (
        echo     ✓ 创建 logs\error
    ) else (
        echo     ✗ 创建 logs\error 失败
    )
) else (
    echo     ✓ logs\error 已存在
)

if not exist "logs\job" (
    mkdir "logs\job" 2>nul
    if exist "logs\job" (
        echo     ✓ 创建 logs\job
    ) else (
        echo     ✗ 创建 logs\job 失败
    )
) else (
    echo     ✓ logs\job 已存在
)

echo.
echo ========================================
echo           目录创建完成！
echo ========================================
echo.
echo 创建的目录结构如下：
echo.
echo ├── logs\
echo │   ├── info\
echo │   ├── error\
echo │   └── job\
echo ├── ssp-jm-supervise\logs\
echo │   ├── info\
echo │   ├── error\
echo │   └── job\
echo └── ssp-jm-bsp\logs\
echo     ├── info\
echo     ├── error\
echo     └── job\
echo.
echo 注意事项：
echo 1. 请确保应用程序对这些目录有写权限
echo 2. 如果某些目录创建失败，请检查是否有足够的权限
echo 3. 可以手动创建失败的目录
echo.
echo 按任意键退出...
pause >nul
