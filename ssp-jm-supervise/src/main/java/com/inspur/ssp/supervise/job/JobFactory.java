package com.inspur.ssp.supervise.job;

import com.alibaba.fastjson.JSONObject;
import com.inspur.ssp.supervise.bean.entity.SupJob;
import com.inspur.ssp.supervise.constant.Constant;
import org.apache.commons.lang.StringUtils;
import org.jangod.iweb.util.DaoFactory;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by cy on 2021/1/8.
 */
@DisallowConcurrentExecution
public class JobFactory implements Job{

    private static Logger logger = LoggerFactory.getLogger(Constant.LOG_TYPE_JOB);

    //默认执行入口
    private static final String METHOD = "execute";

    public JobFactory(){

    }

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        SupJob jobBean = (SupJob)context.getMergedJobDataMap().get("scheduleJob");
        long start = System.currentTimeMillis();
        logger.info("定时任务【"+ jobBean.getName()+"】开始执行，执行时间："+new Date());
        String action = jobBean.getAction();
        String method = jobBean.getMethod();
        if(StringUtils.isEmpty(method)){
            method = METHOD;
        }
        try {
            Class clazz = DaoFactory.getClass(action);
            Method[] methods = clazz.getMethods();//全部方法
            for(Method _method : methods){
                if(method.equals(_method.getName())){
                    Class<?>[] cls = _method.getParameterTypes();
                    if(cls.length == 0){
                        DaoFactory.invoke(action,method,cls,new Object[]{});
                    }else{
                        Map<String,Object> params = (Map<String,Object>)JSONObject.parse(jobBean.getParam());
                        if(null == params) {
                            params = new HashMap<>();
                        }
                        params.put("params",jobBean.getParam());
                        DaoFactory.invoke(action,method,cls,new Object[]{params});
                    }
                }
            }
        } catch (Exception e) {
            logger.error("执行定时任务【"+jobBean.getName()+"】出错,",e);
            // 重新抛出异常，让Quartz知道任务执行失败
            throw new JobExecutionException(e);
        }finally {
            long end = System.currentTimeMillis();
            logger.info("定时任务【"+ jobBean.getName()+"】执行结束，耗时:"+((end-start)/1000)+"s");
        }


    }
}
